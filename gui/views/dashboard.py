import flet as ft
from datetime import datetime
from gui.components.layout import create_page_layout
from gui.config.language import get_text
from gui.config.constants import (
    ROUTE_CLASSES, ROUTE_SUBJECTS, ROUTE_QUIZZES, ROUTE_LOGIN
)

ICON_CLASS = ft.Icons.CLASS_
ICON_BOOK = ft.Icons.BOOK
ICON_QUIZ = ft.Icons.QUIZ

def create_dashboard_view(page: ft.Page):
    # Check if user is authenticated
    current_user = getattr(page.app_state, 'current_user', None)
    if not current_user:
        page.go(ROUTE_LOGIN)
        return ft.View(route="/dashboard", controls=[])

    is_mobile = getattr(page, 'is_mobile', False)
    current_language = getattr(page, 'language', 'en')
    current_time = datetime.now()

    # Modern welcome section with gradient background
    welcome_section = ft.Container(
        content=ft.Column([
            ft.Text(
                f"Welcome, {current_user.get('full_name', current_user.get('username', 'User'))}",
                size=32,
                weight=ft.FontWeight.BOLD,
                color=ft.Colors.WHITE,
                text_align=ft.TextAlign.CENTER
            ),
            ft.Text(
                f"{current_time.strftime('%A, %B %d, %Y')}",
                size=18,
                color=ft.Colors.WHITE70,
                text_align=ft.TextAlign.CENTER
            )
        ],
        spacing=8,
        horizontal_alignment=ft.CrossAxisAlignment.CENTER),
        padding=ft.padding.all(20),
        margin=ft.margin.only(bottom=10, top=0),
        gradient=ft.LinearGradient(
            begin=ft.alignment.top_left,
            end=ft.alignment.bottom_right,
            colors=[ft.Colors.BLUE_600, ft.Colors.PURPLE_600]
        ),
        border_radius=ft.border_radius.all(20),
        alignment=ft.alignment.center
    )

    # Modern dashboard cards with enhanced design
    def create_modern_card(title, value, icon, route=None, color=ft.Colors.BLUE_600):
        card_width = page.width*0.85 if is_mobile else 280

        card = ft.Container(
            content=ft.Column([
                ft.Row([
                    ft.Container(
                        content=ft.Icon(icon, size=28, color=ft.Colors.WHITE),
                        bgcolor=color,
                        padding=ft.padding.all(12),
                        border_radius=ft.border_radius.all(12)
                    ),
                    ft.Column([
                        ft.Text(
                            title,
                            size=16,
                            weight=ft.FontWeight.W_500,
                            color=ft.Colors.BLUE_GREY_200
                        ),
                        ft.Text(
                            value,
                            size=32,
                            weight=ft.FontWeight.BOLD,
                            color=ft.Colors.ON_SURFACE
                        )
                    ], spacing=4, alignment=ft.MainAxisAlignment.CENTER, expand=True)
                ], alignment=ft.MainAxisAlignment.START, spacing=16),
                ft.Container(height=20),
                ft.Container(
                    content=ft.Row([
                        ft.Text(
                            get_text("view_details", current_language),
                            size=14,
                            weight=ft.FontWeight.W_500,
                            color=color
                        ),
                        ft.Icon(ft.Icons.ARROW_FORWARD_IOS, size=14, color=color)
                    ], alignment=ft.MainAxisAlignment.CENTER, spacing=8),
                    padding=ft.padding.symmetric(vertical=8, horizontal=16),
                    border=ft.border.all(1, color),
                    border_radius=ft.border_radius.all(20),
                    on_click=lambda _: page.go(route) if route else None
                )
            ], spacing=0),
            width=card_width,
            padding=ft.padding.all(24),
            bgcolor=ft.Colors.SURFACE,
            border_radius=ft.border_radius.all(16),
            shadow=ft.BoxShadow(
                spread_radius=0,
                blur_radius=10,
                color=ft.Colors.with_opacity(0.1, ft.Colors.BLACK),
                offset=ft.Offset(0, 4)
            ),
            on_click=lambda _: page.go(route) if route else None,
            margin=ft.margin.all(8)
        )
        return card

    # Create modern cards with different colors (without counts)
    classes_card = create_modern_card(
        get_text("classes", current_language),
        "",
        ICON_CLASS,
        ROUTE_CLASSES,
        ft.Colors.BLUE_600
    )

    subjects_card = create_modern_card(
        get_text("subjects", current_language),
        "",
        ICON_BOOK,
        ROUTE_SUBJECTS,
        ft.Colors.ORANGE_600
    )

    quizzes_card = create_modern_card(
        get_text("quizzes", current_language),
        "",
        ICON_QUIZ,
        ROUTE_QUIZZES,
        ft.Colors.PURPLE_600
    )

    # Enhanced layout for cards with better spacing
    if is_mobile:
        cards_layout = ft.Column(
            [
                classes_card,
                subjects_card,
                quizzes_card
            ],
            alignment=ft.MainAxisAlignment.CENTER,
            spacing=16,
            expand=True
        )
    else:
        cards_layout = ft.Row(
            [
                classes_card,
                subjects_card,
                quizzes_card
            ],
            alignment=ft.MainAxisAlignment.CENTER,
            spacing=20,
            wrap=True
        )

    # Create enhanced dashboard content
    content = [
        welcome_section,
        cards_layout
    ]

    return create_page_layout(
        page,
        "",
        content
    )
