"""
Admin dashboard view for managing teachers and viewing system statistics.
"""
import flet as ft
from gui.components.admin_layout import create_admin_page_layout
from gui.services.auth_service import AuthService
from gui.config.constants import ROUTE_LOGIN, ROUTE_ADMIN_TEACHERS
from gui.config.language import get_text

def create_admin_dashboard_view(page: ft.Page):
    """Create the admin dashboard with statistics and teacher management."""
    auth_service = AuthService()
    current_language = getattr(page, 'language', 'en')
    is_mobile = getattr(page, 'is_mobile', False)

    # Check if user is admin
    current_user = getattr(page.app_state, 'current_user', None)
    if not current_user or current_user.get('role') != 'admin':
        page.go(ROUTE_LOGIN)
        return ft.View(route="/admin", controls=[])



    # Modern welcome section with gradient background
    welcome_section = ft.Container(
        content=ft.Column([
            ft.Text(
                f"Bienvenue, {current_user.get('full_name', 'Admin')}",
                size=28,
                weight=ft.FontWeight.BOLD,
                color=ft.Colors.WHITE,
                text_align=ft.TextAlign.CENTER
            ),
            ft.Text(
                "Tableau de Bord Administrateur",
                size=16,
                color=ft.Colors.WHITE70,
                text_align=ft.TextAlign.CENTER
            )
        ],
        spacing=8,
        horizontal_alignment=ft.CrossAxisAlignment.CENTER),
        padding=ft.padding.all(20),
        margin=ft.margin.only(bottom=20, top=0),
        gradient=ft.LinearGradient(
            begin=ft.alignment.top_left,
            end=ft.alignment.bottom_right,
            colors=[ft.Colors.BLUE_600, ft.Colors.PURPLE_600]
        ),
        border_radius=ft.border_radius.all(20),
        alignment=ft.alignment.center,
    )

    # Statistics cards with modern design
    def create_stat_card(title: str, value: str, icon: str, color: str, route: str = None):
        """Create a clickable statistics card."""
        card_width = page.width*0.85 if is_mobile else 280

        def on_card_click(_):
            if route:
                page.go(route)

        # Only show value if it's not empty
        value_controls = []
        if value:
            value_controls.append(ft.Text(
                str(value),
                size=32,
                weight=ft.FontWeight.BOLD,
                color=ft.Colors.ON_SURFACE
            ))

        return ft.Container(
            content=ft.Column([
                ft.Row([
                    ft.Container(
                        content=ft.Icon(icon, size=28, color=ft.Colors.WHITE),
                        bgcolor=color,
                        padding=ft.padding.all(12),
                        border_radius=ft.border_radius.all(12)
                    ),
                    ft.Column([
                        ft.Text(
                            title,
                            size=16,
                            weight=ft.FontWeight.W_500,
                            color=ft.Colors.BLUE_GREY_200
                        ),
                        *value_controls
                    ], spacing=4, alignment=ft.MainAxisAlignment.CENTER, expand=True)
                ], alignment=ft.MainAxisAlignment.START, spacing=16),
                # Add click indicator
                ft.Container(
                    content=ft.Row([
                        ft.Icon(ft.Icons.ARROW_FORWARD_IOS, size=12, color=ft.Colors.BLUE_GREY_400),
                        ft.Text("Cliquer pour voir", size=10, color=ft.Colors.BLUE_GREY_400)
                    ], alignment=ft.MainAxisAlignment.END),
                    margin=ft.margin.only(top=8)
                ) if route else ft.Container()
            ], spacing=0),
            width=card_width,
            padding=ft.padding.all(24),
            bgcolor=ft.Colors.SURFACE,
            border_radius=ft.border_radius.all(16),
            shadow=ft.BoxShadow(
                spread_radius=0,
                blur_radius=10,
                color=ft.Colors.with_opacity(0.1, ft.Colors.BLACK),
                offset=ft.Offset(0, 4)
            ),
            margin=ft.margin.all(8),
            on_click=on_card_click if route else None,
            ink=True if route else False
        )

    # Create statistics cards with French labels and routes (without counts)
    stats_cards = []
    stats_cards.append(create_stat_card("Enseignants", "", ft.Icons.PERSON, ft.Colors.BLUE_600, ROUTE_ADMIN_TEACHERS))
    stats_cards.append(create_stat_card("Classes", "", ft.Icons.SCHOOL, ft.Colors.GREEN_600, "/admin/classes"))
    stats_cards.append(create_stat_card("Matières", "", ft.Icons.BOOK, ft.Colors.PURPLE_600, "/admin/subjects"))

    # Enhanced layout for cards with better spacing
    if is_mobile:
        cards_layout = ft.Column(
            stats_cards,
            alignment=ft.MainAxisAlignment.CENTER,
            spacing=16,
            expand=True
        )
    else:
        cards_layout = ft.Row(
            stats_cards,
            alignment=ft.MainAxisAlignment.CENTER,
            spacing=20,
            wrap=True
        )

    # Add "All Data" button below the cards
    def on_all_data_click(_):
        page.go("/admin/data")
        
    all_data_button = ft.Container(
        content=ft.ElevatedButton(
            text="Toutes les données",
            icon=ft.Icons.DATA_USAGE,
            style=ft.ButtonStyle(
                bgcolor=ft.Colors.BLUE_700,
                color=ft.Colors.WHITE,
                padding=ft.padding.symmetric(horizontal=24, vertical=12),
                shape=ft.RoundedRectangleBorder(radius=12)
            ),
            on_click=on_all_data_click
        ),
        alignment=ft.alignment.center,
        margin=ft.margin.only(top=16, bottom=0)
    )

    # Create enhanced dashboard content
    content = [
        welcome_section,
        cards_layout,
        all_data_button  # Add the button here
    ]

    return create_admin_page_layout(
        page,
        "Tableau de Bord Admin",
        content
    )

